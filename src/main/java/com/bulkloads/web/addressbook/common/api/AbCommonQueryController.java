package com.bulkloads.web.addressbook.common.api;

import java.util.List;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUserRole;
import com.bulkloads.web.addressbook.abuser.service.AbUserService;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserTypeResponse;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/address_book")
@Tag(name = "Address Book")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class AbCommonQueryController {

  private final AbUserService abUserService;

  @GetMapping("/user_roles")
  public List<AbUserRole> getAbUserRoles() {
    return abUserService.getAbUserRoles();
  }

  @GetMapping("/user_types")
  public List<AbUserTypeResponse> getAbUserTypes() {
    return abUserService.getAbUserTypes();
  }

}
