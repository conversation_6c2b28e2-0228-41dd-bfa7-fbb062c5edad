package com.bulkloads.web.addressbook.abuser.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.List;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.addressbook.abuser.service.AbUserService;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(AbUserQueryController.class)
@AutoConfigureMockMvc(addFilters = false)
class AbUserQueryControllerTest extends ControllerTest {

  private static final String URL = TestConstants.AB_BOOK_USERS_URL;
  private static final String AB_USER_RESPONSE_JSON = """
      {
        "ab_user_id" : 1,
        "external_ab_user_id" : "",
        "bl_user_id" : null,
        "ab_company_id" : null,
        "external_ab_company_id" : "",
        "company_name" : "",
        "first_name" : "Test User",
        "last_name" : "",
        "email" : "<EMAIL>",
        "preferred_contact_method" : "",
        "ab_user_notes" : "",
        "location" : "",
        "user_type_ids" : null,
        "user_types" : null,
        "active" : null,
        "bad_email_date" : "",
        "bad_email_reason" : "",
        "ab_user_role_ids" : null,
        "ab_user_roles" : null,
        "modified_date" : "",
        "deleted" : null,
        "last_trailer_user_company_equipment_id" : null,
        "last_truck_user_company_equipment_id" : null,
        "phone_1" : ""
      }
      """;

  @MockBean
  AbUserService service;

  @AfterEach
  void tearDown() {
    verifyNoMoreInteractions(service);
  }

  private AbUserResponse buildAbUserResponse() {
    AbUserResponse response = new AbUserResponse();
    response.setAbUserId(1);
    response.setExternalAbUserId("");
    response.setBlUserId(null);
    response.setAbCompanyId(null);
    response.setExternalAbCompanyId("");
    response.setCompanyName("");
    response.setFirstName("Test User");
    response.setLastName("");
    response.setEmail("<EMAIL>");
    response.setPreferredContactMethod("");
    response.setAbUserNotes("");
    response.setLocation("");
    response.setUserTypeIds(null);
    response.setUserTypes(null);
    response.setActive(null);
    return response;
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetAbUserById_thenShouldBeOk() {
    final int abUserId = 1;
    final AbUserResponse response = buildAbUserResponse();

    when(service.getAbUser(abUserId)).thenReturn(response);

    mockMvc.perform(get(URL + "/{ab_user_id}", abUserId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(AB_USER_RESPONSE_JSON)));

    verify(service).getAbUser(abUserId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenANegativeAbUserId_whenGetAbUserById_thenShouldBeBadRequest() {
    final int abUserId = -1;
    mockMvc.perform(get(URL + "/{ab_user_id}", abUserId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetAbUsers_thenShouldBeOk() {
    final List<AbUserResponse> response = List.of(buildAbUserResponse());

    when(service.getAbUsers(null, null, false, null, false, 0, 100))
        .thenReturn(response);

    mockMvc.perform(get(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson("[ " + AB_USER_RESPONSE_JSON + " ]")));

    verify(service).getAbUsers(null, null, false, null, false, 0, 100);
  }
}
