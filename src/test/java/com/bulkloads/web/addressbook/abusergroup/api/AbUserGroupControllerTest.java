package com.bulkloads.web.addressbook.abusergroup.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import static com.bulkloads.web.confg.TestConstants.DUMMY_ROLE;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.addressbook.abusergroup.service.AbUserGroupService;
import com.bulkloads.web.addressbook.abusergroup.service.dto.AbUserGroupRequest;
import com.bulkloads.web.addressbook.abusergroup.service.dto.AbUserGroupResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(AbUserGroupController.class)
@AutoConfigureMockMvc(addFilters = false)
class AbUserGroupControllerTest extends ControllerTest {

  private static final String URL = TestConstants.AB_BOOK_USER_GROUPS_URL;

  public static final String REQUEST_JSON = """
      {
        "group_name": "Test Group",
        "ab_user_ids": "1,2,3"
      }""";

  public static final String RESPONSE_JSON = """
      {
        "message": "Contact group created",
        "key": 1,
        "data": {
          "ab_user_group_id": 1,
          "group_name": "Test Group",
          "ab_user_ids": "1"
        }
      }""";

  @MockBean
  AbUserGroupService service;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(service);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenCreateAbUserGroup_thenShouldBeOk() {

    final AbUserGroupRequest abUserGroupRequest =
        objectMapper.readValue(REQUEST_JSON, AbUserGroupRequest.class);

    final AbUserGroupResponse userGroupResponse = new AbUserGroupResponse(1, "Test Group", "1");
    when(service.create(abUserGroupRequest)).thenReturn(userGroupResponse);

    mockMvc.perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(RESPONSE_JSON));

    verify(service).create(abUserGroupRequest);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenCreateAbUserGroup_thenShouldBeForbidden() {

    mockMvc.perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(REQUEST_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenCreateAbUserGroup_thenShouldBeUnauthorized() {
    mockMvc.perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(REQUEST_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenDeleteAbUserGroup_thenShouldBeOk() {
    final int abUserGroupId = 1;

    final String responseJson = """
        {
          "message": "Contact group deleted"
        }""";

    doNothing().when(service).deleteById(abUserGroupId);

    mockMvc.perform(delete(URL + "/{ab_user_group_id}", abUserGroupId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(responseJson));

    verify(service).deleteById(abUserGroupId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenDeleteAbUserGroup_thenShouldBeForbidden() {
    final int abUserGroupId = 1;

    mockMvc.perform(delete(URL + "/{ab_user_group_id}", abUserGroupId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenDeleteAbUserGroup_thenShouldBeUnauthorized() {
    final int abUserGroupId = 1;

    mockMvc.perform(delete(URL + "/{ab_user_group_id}", abUserGroupId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenUpdateAbUserGroup_thenShouldBeOk() {
    final int abUserGroupId = 1;
    final AbUserGroupRequest request = objectMapper.readValue(REQUEST_JSON, AbUserGroupRequest.class);
    final AbUserGroupResponse response = new AbUserGroupResponse(1, "1", "Updated Group");

    final String responseJson = """
        {
          "message" : "Contact group updated",
            "key" : 1,
            "data" : {
              "ab_user_group_id" : 1,
              "group_name" : "1",
              "ab_user_ids" : "Updated Group"
            }
        }""";

    when(service.update(abUserGroupId, request)).thenReturn(response);

    mockMvc.perform(put(URL + "/{ab_user_group_id}", abUserGroupId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(prettyPrintJson(responseJson)));

    verify(service).update(abUserGroupId, request);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenUpdateAbUserGroup_thenShouldBeForbidden() {
    final int abUserGroupId = 1;

    mockMvc.perform(put(URL + "/{ab_user_group_id}", abUserGroupId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(REQUEST_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenUpdateAbUserGroup_thenShouldBeUnauthorized() {
    final int abUserGroupId = 1;

    mockMvc.perform(put(URL + "/{ab_user_group_id}", abUserGroupId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(REQUEST_JSON))
        .andExpect(status().isUnauthorized());
  }
}
